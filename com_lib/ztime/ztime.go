package ztime

import (
	"fmt"
	"time"
)

func StartOfDayByStr(tstr string) (time.Time, error) {
	t, err := time.ParseInLocation(time.DateTime, tstr, time.Local)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()), nil
}

func StartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
}
func EndOfDayByStr(tstr string) (time.Time, error) {
	t, err := time.ParseInLocation(time.DateTime, tstr, time.Local)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location()), nil
}
