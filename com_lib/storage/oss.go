package storage

import (
	"bytes"
	"context"
	"mime/multipart"
	"taoqigame/com_util/tencent/tencent_cos"

	"github.com/tencentyun/cos-go-sdk-v5"

	"path/filepath"
)

var _ Storage = &OssStorage{}

type OssStorage struct {
	client *cos.Client
	// BaseUrl string
}

func NewOssStorage() *OssStorage {
	return &OssStorage{
		client: tencent_cos.GetClient(),
	}
}
func (s *OssStorage) Save(path, name string, content []byte) (res string, err error) {
	fullPath := filepath.Join(path, name)

	_, err = s.client.Object.Put(context.Background(), fullPath, bytes.NewReader(content), nil)
	if err != nil {
		return
	}
	return fullPath, err
}
func (s *OssStorage) SaveMultipartFile(path, name string, file *multipart.FileHeader) (res string, err error) {
	fullPath := filepath.Join(path, name)

	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	content := make([]byte, file.Size)
	if _, err := src.Read(content); err != nil {
		return "", err
	}

	_, err = s.client.Object.Put(context.Background(), fullPath, bytes.NewReader(content), nil)
	if err != nil {
		return
	}
	return fullPath, nil
}

func (s *OssStorage) GetFileUrl(path string) string {
	u := s.client.Object.GetObjectURL(path)
	return u.String()
}
