package oss

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"io"
	"mime/multipart"
	"taoqigame/com_err"
	"taoqigame/com_lib/storage"
	"taoqigame/model"

	"gorm.io/gorm"
)

// calculateSHA256 计算内容的SHA256哈希值
func calculateSHA256(content []byte) string {
	hash := sha256.New()
	hash.Write(content)
	return hex.EncodeToString(hash.Sum(nil))
}

// calculateFileSHA256 计算文件的SHA256哈希值
func calculateFileSHA256(file *multipart.FileHeader) (string, error) {
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, src); err != nil {
		return "", err
	}
	return hex.EncodeToString(hash.Sum(nil)), nil
}

// UploadFileByContent 通过内容上传文件
func UploadFileByContent(ctx context.Context, storage storage.Storage, path, name string, content []byte) (fullUrl string, err error) {
	// 计算文件SHA256哈希值
	sha256Hash := calculateSHA256(content)

	// 先检查是否存在相同的path和name的记录
	fileModel := model.NewFile()

	// 根据SHA256哈希值查询是否存在相同的文件
	existingSHA256File, err := fileModel.FindBySHA256(ctx, sha256Hash)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", com_err.NewWarpErr(com_err.ErrCodeDbGet, "查询文件记录失败", err)
	}
	existingPathNameFile, err := fileModel.First(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("path = ? AND name = ?", path, name)
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", com_err.NewWarpErr(com_err.ErrCodeDbGet, "查询文件记录失败", err)
	}
	if existingSHA256File != nil {
		if existingPathNameFile != nil {
			// 更新现有记录的SHA256和URL
			updateData := map[string]interface{}{
				"sha256": sha256Hash,
				"url":    existingSHA256File.URL,
			}
			err = fileModel.Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
				return tx.Where("id = ?", existingPathNameFile.Id)
			}, updateData)
			if err != nil {
				return "", com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "更新文件记录失败", err)
			}
		} else {
			// 创建新的文件记录，使用相同的URL但不同的路径和名称
			newFile := &model.File{
				Path:   path,
				Name:   name,
				URL:    existingSHA256File.URL,
				SHA256: sha256Hash,
			}

			err = newFile.Create(ctx, nil)
		}
		return existingSHA256File.URL, nil

	} else {
		// 如果不存在相同的SHA256，需要重新上传
		filePath, err := storage.Save(path, name, content)
		if err != nil {
			return "", com_err.NewWarpErr(com_err.ErrCodeRequestParam, "文件上传失败", err)
		}

		fullUrl = storage.GetFileUrl(filePath)
		if existingPathNameFile != nil {
			// 更新现有记录的SHA256和URL
			updateData := map[string]interface{}{
				"sha256": sha256Hash,
				"url":    fullUrl,
			}
			err = fileModel.Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
				return tx.Where("id = ?", existingPathNameFile.Id)
			}, updateData)
			if err != nil {
				return "", com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "更新文件记录失败", err)
			}
		} else {
			// 创建新的文件记录，使用相同的URL但不同的路径和名称
			newFile := &model.File{
				Path:   path,
				Name:   name,
				URL:    fullUrl,
				SHA256: sha256Hash,
			}

			err = newFile.Create(ctx, nil)
		}
		return fullUrl, nil
	}
}

// UploadFileByFile 通过文件上传
func UploadFileByFile(ctx context.Context, storage storage.Storage, path, name string, file *multipart.FileHeader) (string, error) {
	// 计算文件SHA256哈希值
	sha256Hash, err := calculateFileSHA256(file)
	if err != nil {
		return "", com_err.NewWarpErr(com_err.ErrCodeRequestParam, "计算文件哈希值失败", err)
	}

	// 先检查是否存在相同的path和name的记录
	fileModel := model.NewFile()

	// 根据SHA256哈希值查询是否存在相同的文件
	existingSHA256File, err := fileModel.FindBySHA256(ctx, sha256Hash)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", com_err.NewWarpErr(com_err.ErrCodeDbGet, "查询文件记录失败", err)
	}
	existingPathNameFile, err := fileModel.First(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("path = ? AND name = ?", path, name)
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", com_err.NewWarpErr(com_err.ErrCodeDbGet, "查询文件记录失败", err)
	}
	if existingSHA256File != nil {
		if existingPathNameFile != nil {
			// 更新现有记录的SHA256和URL
			updateData := map[string]interface{}{
				"sha256": sha256Hash,
				"url":    existingSHA256File.URL,
			}
			err = fileModel.Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
				return tx.Where("id = ?", existingPathNameFile.Id)
			}, updateData)
			if err != nil {
				return "", com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "更新文件记录失败", err)
			}
		} else {
			// 创建新的文件记录，使用相同的URL但不同的路径和名称
			newFile := &model.File{
				Path:   path,
				Name:   name,
				URL:    existingSHA256File.URL,
				SHA256: sha256Hash,
			}

			err = newFile.Create(ctx, nil)
		}
		return existingSHA256File.URL, nil

	} else {
		// 如果不存在相同的SHA256，需要重新上传
		filePath, err := storage.SaveMultipartFile(path, name, file)
		if err != nil {
			return "", com_err.NewWarpErr(com_err.ErrCodeRequestParam, "文件上传失败", err)
		}

		fileURL := storage.GetFileUrl(filePath)
		if existingPathNameFile != nil {
			// 更新现有记录的SHA256和URL
			updateData := map[string]interface{}{
				"sha256": sha256Hash,
				"url":    fileURL,
			}
			err = fileModel.Updates(ctx, nil, func(tx *gorm.DB) *gorm.DB {
				return tx.Where("id = ?", existingPathNameFile.Id)
			}, updateData)
			if err != nil {
				return "", com_err.NewWarpErr(com_err.ErrCodeDbUpdate, "更新文件记录失败", err)
			}
		} else {
			// 创建新的文件记录，使用相同的URL但不同的路径和名称
			newFile := &model.File{
				Path:   path,
				Name:   name,
				URL:    fileURL,
				SHA256: sha256Hash,
			}

			err = newFile.Create(ctx, nil)
		}
		return fileURL, nil
	}
}
