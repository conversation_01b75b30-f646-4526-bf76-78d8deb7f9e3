package http_liveuser

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"strings"
	"taoqigame/com_lib/config"
	"taoqigame/com_lib/ip"
	"taoqigame/com_lib/mdb"
	"taoqigame/com_lib/pay"
	"taoqigame/com_lib/storage"
	"taoqigame/com_lib/xyj"
	"taoqigame/com_util/oss"
	"taoqigame/http/http_liveuser/cron"
	"taoqigame/http/http_liveuser/cron/product"
	"taoqigame/http/http_liveuser/middleware"
	"taoqigame/http/http_liveuser/routers"
	"taoqigame/http/http_liveuser/service"
	"taoqigame/model"

	config2 "github.com/Yeatesss/xinyujian/config"

	"github.com/gin-gonic/gin"
)

func InitBaseSvc() {
	service.InitDbService()

	storage.Engine = storage.NewOssStorage()

}
func Main() {
	// 初始化服务
	InitBaseSvc()
	testImageURL := "https://img.picui.cn/free/2025/06/18/6852bbf9814a3.png"
	imageContent, err := downloadImageFromURL(testImageURL)
	if err != nil {
		log.Fatalf("下载测试图片失败: %v", err)
	}
	fmt.Println(oss.UploadFileByFile(context.Background(), storage.Engine, "TestMaterial/img", "ccb.png", createMultipartFileHeader("ccc.png", imageContent)))
	return
	//gin.SetMode(gin.ReleaseMode)
	initSvc()
	ginEngine := gin.New()
	ginEngine.Use(middleware.Recover())
	ginEngine.Use(middleware.Origin())
	ginEngine.Use(middleware.Authorization())
	ginEngine.Use(middleware.AccountValid())

	// 加载路由配置
	routers.InitRouters(ginEngine)
	// 启动服务
	host := config.GetConfig().GetString("http.host")
	port := config.GetConfig().GetString("http.port")
	// 设置信任的代理服务器
	_ = ginEngine.SetTrustedProxies([]string{host})

	_ = ginEngine.Run(host + ":" + port)
}

func initSvc() {
	pay.InitWechatPay()
	ip.InitCache(config.GetConfig().GetString("redis.host")+":"+config.GetConfig().GetString("redis.port"), config.GetConfig().GetString("redis.auth"), 2)
	mdb.GetEngine().AutoMigrate(
		&model.AdminUser{},
		&model.Anchor{},
		&model.AnchorApplication{},
		&model.AnchorCommentLabel{},
		&model.AnchorPlatform{},
		&model.BusinessUser{},
		&model.FinancialAccount{},
		&model.OperationAccount{},
		&model.OperationLog{},
		&model.Order{},
		&model.Product{},
		&model.ProductDailyStats{},
		&model.ProductReviewReject{},
		&model.RechargeHistory{},
		&model.SmsCode{},
		&model.SystemConfig{},
		&model.Users{},
		&model.TransactionHistory{},
		&model.Comment{},
		&model.ExternalOrder{},
		&model.AnchorPaymentAccount{},
		&model.XinmeetSettleHistory{},
		&model.File{},
	)
	var withs = []xyj.Option{
		xyj.WithCompanyInfo, xyj.WithCompanyItem, xyj.WithCompanyTask,
	}
	withs = append(withs, func(cli *xyj.Client) error {
		cli.AlipayAcctNo = config.GetConfig().GetString("xinYuJian.aliAccNo")
		return nil
	}, func(cli *xyj.Client) error {
		cli.BankAcctNo = config.GetConfig().GetString("xinYuJian.bankAccNo")
		return nil
	})
	xyj.InitClient(config.GetConfig().GetString("xinYuJian.businessID"), config2.NewConfig().
		SetAppID(config.GetConfig().GetString("xinYuJian.appID")).
		SetPrivateKey(config.GetConfig().GetString("xinYuJian.privateKey")).
		SetPlatformPubKey(config.GetConfig().GetString("xinYuJian.platformPubKey")).
		SetDebug(config.GetConfig().GetBool("xinYuJian.debug")). // 开启调试模式
		SetPrintCurl(config.GetConfig().GetBool("xinYuJian.printCurl")). // 开启打印curl命令
		SetBaseURL(strings.TrimRight(config.GetConfig().GetString("xinYuJian.baseUrl"), "/")).SetCallbackURL(config.GetConfig().GetString("xinYuJian.Callback")), withs...)
	cron.Run()
	product.Popularity() // 更新产品人气
}
func createMultipartFileHeader(filename string, content []byte) *multipart.FileHeader {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil
	}

	part.Write(content)
	writer.Close()

	// 解析multipart数据
	reader := multipart.NewReader(body, writer.Boundary())
	form, err := reader.ReadForm(int64(len(content)) + 1024)
	if err != nil {
		return nil
	}

	if files, ok := form.File["file"]; ok && len(files) > 0 {
		return files[0]
	}

	return nil
}

// downloadImageFromURL 从URL下载图片内容
func downloadImageFromURL(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}
